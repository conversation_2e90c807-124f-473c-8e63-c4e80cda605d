/* eslint-disable @typescript-eslint/no-explicit-any */
import { AnalysisResult } from '@/services/aiService/enhancedAIService';
import { useState, useCallback } from 'react';

export interface ModelInfo {
  id: string;
  fileName: string;
  filePath?: string;
  fileSize?: number;
  uploadedAt: string;
  updatedAt: string;
  analysis?: AnalysisInfo | null;
  _count?: {
    prompts: number;
    modelStates: number;
  };
}

export interface AnalysisInfo {
  id: string;
  analysisType: string;
  result: AnalysisResult;
  metadata?: Record<string, any>;
  createdAt: string;
}

export interface PromptInfo {
  id: string;
  userPrompt: string;
  aiResponse?: string;
  promptType: string;
  status: string;
  metadata?: Record<string, any>;
  createdAt: string;
  updatedAt: string;
}

export interface ObjectState {
  objectId: string;
  objectName?: string;
  position: { x: number; y: number; z: number };
  rotation: { x: number; y: number; z: number };
  scale: { x: number; y: number; z: number };
  visible: boolean;
  selected: boolean;
  properties?: Record<string, any>;
}

export const useModelDatabase = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Создать или получить модель
  const createOrGetModel = useCallback(
    async (
      fileName: string,
      filePath?: string,
      fileSize?: number,
    ): Promise<ModelInfo | null> => {
      setLoading(true);
      setError(null);

      try {
        const response = await fetch('/api/models', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            fileName,
            filePath,
            fileSize,
          }),
        });

        if (!response.ok) {
          throw new Error(`Failed to create/get model: ${response.statusText}`);
        }

        const model = await response.json();
        return model;
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : 'Unknown error';
        setError(errorMessage);
        console.error('Error creating/getting model:', err);
        return null;
      } finally {
        setLoading(false);
      }
    },
    [],
  );

  // Получить модель по имени файла
  const getModelByFileName = useCallback(
    async (fileName: string): Promise<ModelInfo | null> => {
      setLoading(true);
      setError(null);

      try {
        const encodedFileName = encodeURIComponent(fileName);
        const response = await fetch(`/api/models/${encodedFileName}`);

        if (response.status === 404) {
          return null;
        }

        if (!response.ok) {
          throw new Error(`Failed to get model: ${response.statusText}`);
        }

        const model = await response.json();
        return model;
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : 'Unknown error';
        setError(errorMessage);
        console.error('Error getting model:', err);
        return null;
      } finally {
        setLoading(false);
      }
    },
    [],
  );

  // Получить все модели
  const getAllModels = useCallback(async (): Promise<ModelInfo[]> => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/models');

      if (!response.ok) {
        throw new Error(`Failed to get models: ${response.statusText}`);
      }

      const models = await response.json();
      return models;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      setError(errorMessage);
      console.error('Error getting models:', err);
      return [];
    } finally {
      setLoading(false);
    }
  }, []);

  // Сохранить анализ модели
  const saveAnalysis = useCallback(
    async (
      fileName: string,
      analysisType: string,
      result: any,
      metadata?: any,
    ): Promise<AnalysisInfo | null> => {
      setLoading(true);
      setError(null);

      try {
        const encodedFileName = encodeURIComponent(fileName);
        const response = await fetch(
          `/api/models/${encodedFileName}/analysis`,
          {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              analysisType,
              result,
              metadata,
            }),
          },
        );

        if (!response.ok) {
          throw new Error(`Failed to save analysis: ${response.statusText}`);
        }

        const analysis = await response.json();
        return analysis;
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : 'Unknown error';
        setError(errorMessage);
        console.error('Error saving analysis:', err);
        return null;
      } finally {
        setLoading(false);
      }
    },
    [],
  );

  // Получить анализ модели
  const getAnalysis = useCallback(
    async (
      fileName: string,
      analysisType?: string,
    ): Promise<AnalysisInfo | null> => {
      setLoading(true);
      setError(null);

      try {
        const encodedFileName = encodeURIComponent(fileName);
        const url = new URL(
          `/api/models/${encodedFileName}/analysis`,
          window.location.origin,
        );
        if (analysisType) {
          url.searchParams.set('type', analysisType);
        }

        const response = await fetch(url.toString());

        if (response.status === 404) {
          return null;
        }

        if (!response.ok) {
          throw new Error(`Failed to get analysis: ${response.statusText}`);
        }

        const analysis = await response.json();
        return analysis;
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : 'Unknown error';
        setError(errorMessage);
        console.error('Error getting analysis:', err);
        return null;
      } finally {
        setLoading(false);
      }
    },
    [],
  );

  // Сохранить промпт
  const savePrompt = useCallback(
    async (
      fileName: string,
      userPrompt: string,
      promptType: string,
      metadata?: any,
    ): Promise<PromptInfo | null> => {
      setLoading(true);
      setError(null);

      try {
        const encodedFileName = encodeURIComponent(fileName);
        const response = await fetch(`/api/models/${encodedFileName}/prompts`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            userPrompt,
            promptType,
            metadata,
          }),
        });

        if (!response.ok) {
          throw new Error(`Failed to save prompt: ${response.statusText}`);
        }

        const prompt = await response.json();
        return prompt;
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : 'Unknown error';
        setError(errorMessage);
        console.error('Error saving prompt:', err);
        return null;
      } finally {
        setLoading(false);
      }
    },
    [],
  );

  // Получить историю промптов
  const getPromptHistory = useCallback(
    async (fileName: string, limit: number = 10): Promise<PromptInfo[]> => {
      setLoading(true);
      setError(null);

      try {
        const encodedFileName = encodeURIComponent(fileName);
        const url = new URL(
          `/api/models/${encodedFileName}/prompts`,
          window.location.origin,
        );
        url.searchParams.set('limit', limit.toString());

        const response = await fetch(url.toString());

        if (!response.ok) {
          throw new Error(
            `Failed to get prompt history: ${response.statusText}`,
          );
        }

        const prompts = await response.json();
        return prompts;
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : 'Unknown error';
        setError(errorMessage);
        console.error('Error getting prompt history:', err);
        return [];
      } finally {
        setLoading(false);
      }
    },
    [],
  );

  // Сохранить состояние модели
  const saveModelState = useCallback(
    async (fileName: string, objectStates: ObjectState[]): Promise<boolean> => {
      setLoading(true);
      setError(null);

      try {
        const encodedFileName = encodeURIComponent(fileName);
        const response = await fetch(`/api/models/${encodedFileName}/state`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            objectStates,
          }),
        });

        if (!response.ok) {
          throw new Error(`Failed to save model state: ${response.statusText}`);
        }

        return true;
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : 'Unknown error';
        setError(errorMessage);
        console.error('Error saving model state:', err);
        return false;
      } finally {
        setLoading(false);
      }
    },
    [],
  );

  // Получить состояние модели
  const getModelState = useCallback(
    async (fileName: string): Promise<ObjectState[]> => {
      setLoading(true);
      setError(null);

      try {
        const encodedFileName = encodeURIComponent(fileName);
        const response = await fetch(`/api/models/${encodedFileName}/state`);

        if (!response.ok) {
          throw new Error(`Failed to get model state: ${response.statusText}`);
        }

        const state = await response.json();
        return state;
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : 'Unknown error';
        setError(errorMessage);
        console.error('Error getting model state:', err);
        return [];
      } finally {
        setLoading(false);
      }
    },
    [],
  );

  // Удалить модель
  const deleteModel = useCallback(
    async (fileName: string): Promise<boolean> => {
      setLoading(true);
      setError(null);

      try {
        const encodedFileName = encodeURIComponent(fileName);
        const response = await fetch(`/api/models/${encodedFileName}`, {
          method: 'DELETE',
        });

        if (!response.ok) {
          throw new Error(`Failed to delete model: ${response.statusText}`);
        }

        return true;
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : 'Unknown error';
        setError(errorMessage);
        console.error('Error deleting model:', err);
        return false;
      } finally {
        setLoading(false);
      }
    },
    [],
  );

  return {
    loading,
    error,
    createOrGetModel,
    getModelByFileName,
    getAllModels,
    saveAnalysis,
    getAnalysis,
    savePrompt,
    getPromptHistory,
    saveModelState,
    getModelState,
    deleteModel,
  };
};
