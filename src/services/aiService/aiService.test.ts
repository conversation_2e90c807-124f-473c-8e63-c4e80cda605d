import * as THREE from 'three';
import { EnhancedAIService } from '@/app/services/enhancedAIService';

// Mock THREE.Box3
jest.mock('three', () => {
  const originalThree = jest.requireActual('three');
  return {
    ...originalThree,
    Box3: jest.fn().mockImplementation(() => ({
      setFromObject: jest.fn().mockReturnThis(),
      getSize: jest.fn().mockReturnValue(new originalThree.Vector3(1, 1, 1)),
      getCenter: jest.fn().mockReturnValue(new originalThree.Vector3(0, 0, 0)),
      isEmpty: jest.fn().mockReturnValue(false),
    })),
  };
});

// Mock fetch for API calls
global.fetch = jest.fn();

describe('EnhancedAIService', () => {
  let aiService: EnhancedAIService;
  let mockScene: THREE.Scene;
  let mockMesh: THREE.Mesh;

  beforeEach(() => {
    aiService = new EnhancedAIService();
    mockScene = new THREE.Scene();
    mockMesh = new THREE.Mesh(
      new THREE.BoxGeometry(1, 1, 1),
      new THREE.MeshBasicMaterial({ color: 0xff0000 }),
    );
    mockMesh.uuid = 'test-uuid';
    mockScene.add(mockMesh);

    // Reset mocks
    (global.fetch as jest.Mock).mockReset();
  });

  describe('analyzeScene', () => {
    it('should analyze scene with fallback when requested', async () => {
      await aiService.analyzeScene(mockScene, true);

      // Verify object was registered with a name
      const registeredObjects = aiService.getRegisteredObjects();
      expect(registeredObjects.length).toBeGreaterThan(0);
      expect(registeredObjects[0].uuid).toBe('test-uuid');
    });

    it('should attempt to use AI analysis', async () => {
      // Mock successful API response
      const mockResponse = {
        content: [
          {
            type: 'text',
            text: '```json\n{"objects":[{"uuid":"test-uuid","name":"cube","category":"furniture","confidence":90}]}\n```',
          },
        ],
      };

      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: jest.fn().mockResolvedValueOnce(mockResponse),
      });

      await aiService.analyzeScene(mockScene);

      // Verify object was registered
      const registeredObjects = aiService.getRegisteredObjects();
      expect(registeredObjects.length).toBeGreaterThan(0);
      expect(registeredObjects[0].uuid).toBe('test-uuid');
    });

    it('should fall back to basic analysis if AI analysis fails', async () => {
      // Mock failed API response
      (global.fetch as jest.Mock).mockRejectedValueOnce(new Error('API error'));

      await aiService.analyzeScene(mockScene);

      // Verify object was registered with a name
      const registeredObjects = aiService.getRegisteredObjects();
      expect(registeredObjects.length).toBeGreaterThan(0);
    });
  });
});
