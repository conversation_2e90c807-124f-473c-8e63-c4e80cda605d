export const getMaxTokens = () => {
  const tokens = process.env.NEXT_PUBLIC_ANALYSIS_MAX_TOKENS;
  if (!tokens) {
    throw new Error('NEXT_PUBLIC_ANALYSIS_MAX_TOKENS is not set');
  }
  return parseInt(tokens, 10);
};

export const getBatchSize = () => {
  const batchSize = process.env.NEXT_PUBLIC_ANALYSIS_BATCH_SIZE;
  if (!batchSize) {
    return 50; // Default batch size
  }
  return parseInt(batchSize, 10);
};
