import { NextRequest, NextResponse } from 'next/server';
import { ModelService } from '@/services/modelService';
import type { Model, ModelAnalysis } from '@/generated/prisma';

// Type for model with analysis relation
type ModelWithAnalysis = Model & {
  analysis?: ModelAnalysis | null;
};

// GET /api/models/[fileName]/analysis - получить анализ модели
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ fileName: string }> },
) {
  try {
    const { fileName } = await params;
    const decodedFileName = decodeURIComponent(fileName);
    const { searchParams } = new URL(request.url);
    const analysisType = searchParams.get('type');

    const model = (await ModelService.getModelByFileName(
      decodedFileName,
    )) as ModelWithAnalysis | null;
    if (!model) {
      return NextResponse.json({ error: 'Model not found' }, { status: 404 });
    }

    const analysis = model.analysis;

    if (!analysis) {
      return NextResponse.json(
        { error: 'Analysis not found' },
        { status: 404 },
      );
    }

    // Если указан тип анализа, проверяем соответствие
    if (analysisType && analysis.analysisType !== analysisType) {
      return NextResponse.json(
        { error: 'Analysis not found' },
        { status: 404 },
      );
    }

    return NextResponse.json({
      ...analysis,
      result: JSON.parse(analysis.result),
      metadata: analysis.metadata ? JSON.parse(analysis.metadata) : null,
    });
  } catch (error) {
    console.error('Error fetching analysis:', error);
    return NextResponse.json(
      { error: 'Failed to fetch analysis' },
      { status: 500 },
    );
  }
}

// POST /api/models/[fileName]/analysis - сохранить результат анализа
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ fileName: string }> },
) {
  try {
    const { fileName } = await params;
    const decodedFileName = decodeURIComponent(fileName);
    const body = await request.json();
    const { analysisType, result, metadata } = body;

    if (!analysisType || !result) {
      return NextResponse.json(
        { error: 'analysisType and result are required' },
        { status: 400 },
      );
    }

    const model = (await ModelService.getModelByFileName(
      decodedFileName,
    )) as ModelWithAnalysis | null;
    if (!model) {
      return NextResponse.json({ error: 'Model not found' }, { status: 404 });
    }

    const analysis = await ModelService.saveAnalysis(model.id, {
      analysisType,
      result,
      metadata,
    });

    return NextResponse.json({
      ...analysis,
      result: JSON.parse(analysis.result),
      metadata: analysis.metadata ? JSON.parse(analysis.metadata) : null,
    });
  } catch (error) {
    console.error('Error saving analysis:', error);
    return NextResponse.json(
      { error: 'Failed to save analysis' },
      { status: 500 },
    );
  }
}
