# Пример интеграции базы данных с ThreeViewerContainer

Этот файл показывает, как интегрировать новую систему базы данных с существующим ThreeViewerContainer.

## 1. Обновление ThreeViewerContainer

```typescript
// src/components/ThreeViewerContainer/index.tsx

import { useState, useRef, useCallback, useEffect } from 'react';
import { EnhancedAIService } from '@/lib/enhancedAIService';
import { useModelDatabase } from '@/hooks/useModelDatabase';
import DatabaseIntegration from './DatabaseIntegration';
import ModelHistoryPanel from './ModelHistoryPanel';

export default function ThreeViewerContainer() {
  // Существующие состояния...
  const [showHistory, setShowHistory] = useState(false);
  const [currentFileName, setCurrentFileName] = useState<string | null>(null);
  const [databaseInitialized, setDatabaseInitialized] = useState(false);
  
  // Refs для интеграции с базой данных
  const enhancedAIServiceRef = useRef<EnhancedAIService | null>(null);
  
  // Инициализация EnhancedAIService
  useEffect(() => {
    enhancedAIServiceRef.current = new EnhancedAIService(aiServiceType);
  }, [aiServiceType]);

  // Обработчик загрузки файла с интеграцией БД
  const handleFileUpload = useCallback(async (file: File) => {
    try {
      setIsLoading(true);
      setError(null);
      
      // Существующая логика загрузки файла...
      const url = URL.createObjectURL(file);
      const fileType = file.name.split('.').pop()?.toLowerCase() || '';
      
      setModelData({ url, fileType });
      setCurrentFileName(file.name);
      
      // Инициализация модели в базе данных
      if (enhancedAIServiceRef.current) {
        await enhancedAIServiceRef.current.setCurrentModel(
          file.name,
          url,
          file.size
        );
        setDatabaseInitialized(true);
      }
      
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to upload file');
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Обработчик анализа модели с сохранением в БД
  const handleAnalyzeModel = useCallback(async (useFallback: boolean = false) => {
    if (!sceneSetupRef.current || !enhancedAIServiceRef.current || !databaseInitialized) {
      return;
    }

    try {
      setIsAnalyzing(true);
      setAnalysisError(false);

      // Анализ с сохранением в базу данных
      const analysisId = await enhancedAIServiceRef.current.analyzeSceneWithDB(
        sceneSetupRef.current.scene,
        useFallback
      );

      setModelAnalyzed(true);
      setAnalysisNotification(
        `Model analysis complete and saved (ID: ${analysisId}). AI assistant is now ready.`
      );

      setTimeout(() => setAnalysisNotification(null), 5000);

    } catch (error) {
      console.error('Error analyzing model:', error);
      setAnalysisError(true);
      setError(error instanceof Error ? error.message : 'Analysis failed');
    } finally {
      setIsAnalyzing(false);
    }
  }, [databaseInitialized]);

  // Обработчик AI промпта с сохранением в БД
  const handleSubmitPrompt = useCallback(async (prompt: string) => {
    if (!sceneSetupRef.current || !enhancedAIServiceRef.current || !databaseInitialized || isProcessingAI) {
      return;
    }

    setIsProcessingAI(true);

    try {
      // Сохранить состояние перед изменениями
      historyManagerRef.current.addState(
        sceneSetupRef.current.scene,
        `Before AI prompt: "${prompt}"`
      );

      // Обработать промпт с сохранением в БД
      const response = await enhancedAIServiceRef.current.processPromptWithDB(
        prompt,
        sceneSetupRef.current.scene,
        'modification'
      );

      // Применить изменения к сцене
      enhancedAIServiceRef.current.applyChanges(
        sceneSetupRef.current.scene,
        response.changes
      );

      // Сохранить состояние после изменений
      historyManagerRef.current.addState(
        sceneSetupRef.current.scene,
        `After AI prompt: "${prompt}"`
      );

      console.log(`Prompt processed and saved with ID: ${response.promptId}`);

    } catch (error) {
      console.error('Error processing prompt:', error);
      setError(error instanceof Error ? error.message : 'AI processing failed');
    } finally {
      setIsProcessingAI(false);
    }
  }, [databaseInitialized, isProcessingAI]);

  return (
    <div className="w-full h-screen flex flex-col bg-gray-100">
      {/* Существующий UI... */}
      
      {/* Новая кнопка для истории модели */}
      {currentFileName && (
        <button
          onClick={() => setShowHistory(true)}
          className="absolute top-4 right-4 z-10 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
        >
          Model History
        </button>
      )}

      {/* Панель истории модели */}
      <ModelHistoryPanel
        currentFileName={currentFileName}
        isVisible={showHistory}
        onClose={() => setShowHistory(false)}
      />

      {/* Интеграция с базой данных */}
      <DatabaseIntegration
        sceneSetupRef={sceneSetupRef}
        modelRef={modelRef}
        aiServiceType={aiServiceType}
        currentFileName={currentFileName}
        onAnalysisComplete={(analysisId) => {
          console.log('Analysis completed:', analysisId);
        }}
        onPromptComplete={(promptId) => {
          console.log('Prompt completed:', promptId);
        }}
        onStateLoaded={() => {
          console.log('Model state loaded from database');
        }}
      />
    </div>
  );
}
```

## 2. Обновление AI панели

```typescript
// src/components/ThreeViewerContainer/AIPromptPanel.tsx

import { useState, useEffect } from 'react';
import { useModelDatabase } from '@/hooks/useModelDatabase';

interface AIPromptPanelProps {
  // Существующие пропсы...
  currentFileName: string | null;
}

export default function AIPromptPanel({
  // Существующие пропсы...
  currentFileName,
}: AIPromptPanelProps) {
  const [promptHistory, setPromptHistory] = useState<any[]>([]);
  const { getPromptHistory } = useModelDatabase();

  // Загрузка истории промптов
  useEffect(() => {
    const loadHistory = async () => {
      if (currentFileName) {
        const history = await getPromptHistory(currentFileName, 5);
        setPromptHistory(history);
      }
    };

    loadHistory();
  }, [currentFileName, getPromptHistory]);

  return (
    <div className="bg-white rounded-lg shadow-lg p-4">
      {/* Существующий UI для ввода промпта... */}
      
      {/* Новая секция с историей промптов */}
      {promptHistory.length > 0 && (
        <div className="mt-4 border-t pt-4">
          <h4 className="text-sm font-medium text-gray-700 mb-2">Recent Prompts</h4>
          <div className="space-y-2 max-h-32 overflow-y-auto">
            {promptHistory.map((prompt) => (
              <div key={prompt.id} className="text-xs p-2 bg-gray-50 rounded">
                <div className="font-medium text-gray-800">{prompt.userPrompt}</div>
                <div className="text-gray-500 mt-1">
                  {new Date(prompt.createdAt).toLocaleString()} - {prompt.status}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
```

## 3. Автосохранение состояния

```typescript
// src/components/ThreeViewerContainer/AutoSave.tsx

import { useEffect, useRef } from 'react';
import { useModelDatabase } from '@/hooks/useModelDatabase';
import type { SceneSetup } from './utils/sceneSetup';

interface AutoSaveProps {
  sceneSetupRef: React.RefObject<SceneSetup | null>;
  currentFileName: string | null;
  enabled: boolean;
  interval?: number; // в миллисекундах
}

export default function AutoSave({
  sceneSetupRef,
  currentFileName,
  enabled,
  interval = 30000, // 30 секунд по умолчанию
}: AutoSaveProps) {
  const { saveModelState } = useModelDatabase();
  const lastSaveRef = useRef<number>(0);

  useEffect(() => {
    if (!enabled || !currentFileName || !sceneSetupRef.current) {
      return;
    }

    const saveState = async () => {
      if (!sceneSetupRef.current) return;

      try {
        const objectStates: any[] = [];
        
        sceneSetupRef.current.scene.traverse(object => {
          if (object instanceof THREE.Mesh && object.uuid) {
            objectStates.push({
              objectId: object.uuid,
              objectName: object.name || undefined,
              position: {
                x: object.position.x,
                y: object.position.y,
                z: object.position.z
              },
              rotation: {
                x: object.rotation.x,
                y: object.rotation.y,
                z: object.rotation.z
              },
              scale: {
                x: object.scale.x,
                y: object.scale.y,
                z: object.scale.z
              },
              visible: object.visible,
              selected: false,
              properties: {
                materialType: object.material?.type,
                geometryType: object.geometry?.type,
              }
            });
          }
        });

        await saveModelState(currentFileName, objectStates);
        lastSaveRef.current = Date.now();
        console.log('Auto-saved model state');

      } catch (error) {
        console.error('Auto-save failed:', error);
      }
    };

    const intervalId = setInterval(saveState, interval);

    return () => {
      clearInterval(intervalId);
    };
  }, [enabled, currentFileName, interval, saveModelState]);

  return null; // Этот компонент не рендерит UI
}
```

## 4. Использование в главном компоненте

```typescript
// src/components/ThreeViewerContainer/index.tsx

import AutoSave from './AutoSave';

export default function ThreeViewerContainer() {
  // ... существующий код ...

  return (
    <div className="w-full h-screen flex flex-col bg-gray-100">
      {/* Существующий UI... */}

      {/* Автосохранение */}
      <AutoSave
        sceneSetupRef={sceneSetupRef}
        currentFileName={currentFileName}
        enabled={databaseInitialized && modelAnalyzed}
        interval={30000} // 30 секунд
      />

      {/* Остальные компоненты... */}
    </div>
  );
}
```

## Преимущества интеграции

1. **Персистентность данных** - Все анализы, промпты и состояния моделей сохраняются между сессиями
2. **История взаимодействий** - Полная история работы с каждой моделью
3. **Восстановление состояния** - Автоматическое восстановление последнего состояния модели
4. **Аналитика** - Возможность анализа паттернов использования AI
5. **Резервное копирование** - Все данные сохраняются в локальной базе данных

## Следующие шаги

1. Интегрировать DatabaseIntegration в существующий ThreeViewerContainer
2. Добавить кнопку "Model History" в UI
3. Обновить AI панель для отображения истории промптов
4. Добавить автосохранение состояния
5. Протестировать полную интеграцию с реальными 3D моделями
