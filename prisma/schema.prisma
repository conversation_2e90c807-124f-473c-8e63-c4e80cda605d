generator client {
  provider = "prisma-client-js"
  output   = "../src/generated/prisma"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

model Model {
  id          String         @id @default(cuid())
  fileName    String         @unique
  filePath    String?
  fileSize    Int?
  uploadedAt  DateTime       @default(now())
  updatedAt   DateTime       @updatedAt
  analysis    ModelAnalysis?
  modelStates ModelState[]
  prompts     Prompt[]

  @@map("models")
}

model ModelAnalysis {
  id           String   @id @default(cuid())
  modelId      String   @unique
  analysisType String
  result       String
  metadata     String?
  createdAt    DateTime @default(now())
  model        Model    @relation(fields: [modelId], references: [id], onDelete: Cascade)

  @@map("model_analysis")
}

model Prompt {
  id         String   @id @default(cuid())
  modelId    String
  userPrompt String
  aiResponse String?
  promptType String
  status     String   @default("pending")
  metadata   String?
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
  model      Model    @relation(fields: [modelId], references: [id], onDelete: Cascade)

  @@map("prompts")
}

model ModelState {
  id         String   @id @default(cuid())
  modelId    String
  objectId   String
  objectName String?
  position   String
  rotation   String
  scale      String
  visible    Boolean  @default(true)
  selected   Boolean  @default(false)
  properties String?
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
  model      Model    @relation(fields: [modelId], references: [id], onDelete: Cascade)

  @@unique([modelId, objectId])
  @@map("model_states")
}
