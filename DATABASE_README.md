# База данных для ThreeJS Viewer

Этот проект теперь включает полнофункциональную базу данных для хранения результатов анализа моделей, промптов и текущего состояния 3D моделей.

## Структура базы данных

### Таблицы

1. **models** - Информация о загруженных 3D моделях
   - `id` - Уникальный идентификатор
   - `fileName` - Название файла модели (ключ для мэтчинга)
   - `filePath` - Путь к файлу
   - `fileSize` - Размер файла в байтах
   - `uploadedAt` - Дата загрузки
   - `updatedAt` - Дата последнего обновления

2. **model_analyses** - Результаты анализа моделей (один анализ на модель)
   - `id` - Уникальный идентификатор
   - `modelId` - Уникальная ссылка на модель (один анализ на модель)
   - `analysisType` - Тип анализа (например, "ai_analysis", "geometry_analysis")
   - `result` - JSON строка с результатами анализа
   - `metadata` - Дополнительные метаданные в JSON
   - `createdAt` - Дата создания

3. **prompts** - История промптов пользователя
   - `id` - Уникальный идентификатор
   - `modelId` - Ссылка на модель
   - `userPrompt` - Промпт пользователя
   - `aiResponse` - Ответ AI
   - `promptType` - Тип промпта (например, "analysis", "modification", "question")
   - `status` - Статус (pending, completed, failed)
   - `metadata` - Дополнительные данные в JSON
   - `createdAt` - Дата создания
   - `updatedAt` - Дата обновления

4. **model_states** - Текущее состояние объектов в модели
   - `id` - Уникальный идентификатор
   - `modelId` - Ссылка на модель
   - `objectId` - ID объекта в 3D сцене
   - `objectName` - Название объекта
   - `position` - JSON с координатами позиции {x, y, z}
   - `rotation` - JSON с углами поворота {x, y, z}
   - `scale` - JSON с масштабом {x, y, z}
   - `visible` - Видимость объекта
   - `selected` - Выбран ли объект
   - `properties` - Дополнительные свойства объекта в JSON
   - `createdAt` - Дата создания
   - `updatedAt` - Дата обновления

## Установка и настройка

### 1. Установка зависимостей

```bash
npm install prisma @prisma/client
```

### 2. Инициализация базы данных

```bash
npx prisma migrate dev --name init
```

### 3. Генерация Prisma Client

```bash
npx prisma generate
```

## API Endpoints

### Модели

- `GET /api/models` - Получить все модели
- `POST /api/models` - Создать или получить модель
- `GET /api/models/[fileName]` - Получить модель по имени файла
- `DELETE /api/models/[fileName]` - Удалить модель

### Анализ

- `GET /api/models/[fileName]/analysis` - Получить анализ модели
- `POST /api/models/[fileName]/analysis` - Сохранить результат анализа

### Промпты

- `GET /api/models/[fileName]/prompts` - Получить историю промптов
- `POST /api/models/[fileName]/prompts` - Создать новый промпт
- `PUT /api/prompts/[promptId]` - Обновить промпт с ответом AI

### Состояние модели

- `GET /api/models/[fileName]/state` - Получить состояние модели
- `POST /api/models/[fileName]/state` - Сохранить состояние модели
- `PUT /api/models/[fileName]/state` - Обновить состояние конкретного объекта

## Использование в коде

### 1. ModelService

Основной сервис для работы с базой данных:

```typescript
import { ModelService } from '@/lib/modelService';

// Создать или получить модель
const model = await ModelService.getOrCreateModel({
  fileName: 'model.glb',
  filePath: '/uploads/model.glb',
  fileSize: 1024000
});

// Сохранить анализ
await ModelService.saveAnalysis(model.id, {
  analysisType: 'scene_analysis',
  result: { objectCount: 5, objects: [...] },
  metadata: { useFallback: false }
});

// Сохранить промпт
const prompt = await ModelService.savePrompt(model.id, {
  userPrompt: 'Move the chair to the left',
  promptType: 'modification'
});
```

### 2. EnhancedAIService

Расширенный AI сервис с интеграцией базы данных:

```typescript
import { EnhancedAIService } from '@/lib/enhancedAIService';

const aiService = new EnhancedAIService('claude');

// Установить текущую модель
await aiService.setCurrentModel('model.glb');

// Анализ с сохранением в БД
const analysisId = await aiService.analyzeSceneWithDB(scene);

// Обработка промпта с сохранением в БД
const response = await aiService.processPromptWithDB(
  'Move the chair to the left',
  scene,
  'modification'
);
```

### 3. React Hook

Хук для удобной работы с базой данных в React компонентах:

```typescript
import { useModelDatabase } from '@/hooks/useModelDatabase';

const {
  loading,
  error,
  createOrGetModel,
  getPromptHistory,
  saveModelState
} = useModelDatabase();

// Создать модель
const model = await createOrGetModel('model.glb');

// Получить историю промптов
const prompts = await getPromptHistory('model.glb', 10);
```

## Компоненты

### 1. DatabaseIntegration

Компонент для интеграции базы данных с ThreeViewer:

```typescript
import DatabaseIntegration from '@/components/ThreeViewerContainer/DatabaseIntegration';

// В родительском компоненте
const databaseIntegration = DatabaseIntegration({
  sceneSetupRef,
  modelRef,
  aiServiceType: 'claude',
  currentFileName: 'model.glb',
  onAnalysisComplete: (analysisId) => console.log('Analysis completed:', analysisId),
  onPromptComplete: (promptId) => console.log('Prompt completed:', promptId)
});
```

### 2. ModelHistoryPanel

Панель для отображения истории модели:

```typescript
import ModelHistoryPanel from '@/components/ThreeViewerContainer/ModelHistoryPanel';

<ModelHistoryPanel
  currentFileName="model.glb"
  isVisible={showHistory}
  onClose={() => setShowHistory(false)}
/>
```

## Особенности

### Мэтчинг по имени файла

Все данные модели мэтчатся по названию файла модели (`fileName`). Это позволяет:
- Легко находить данные для конкретной модели
- Избегать дублирования данных при повторной загрузке той же модели
- Поддерживать связность данных между сессиями

### Автосохранение состояния

DatabaseIntegration компонент автоматически сохраняет состояние модели каждые 30 секунд.

### Восстановление состояния

При загрузке модели автоматически восстанавливается последнее сохраненное состояние объектов.

## Миграции

Для изменения схемы базы данных:

```bash
# Создать новую миграцию
npx prisma migrate dev --name migration_name

# Применить миграции в продакшене
npx prisma migrate deploy

# Сбросить базу данных (только для разработки)
npx prisma migrate reset
```

## Резервное копирование

База данных SQLite сохраняется в файле `prisma/dev.db`. Для резервного копирования просто скопируйте этот файл.

## Мониторинг

Для просмотра данных в базе используйте Prisma Studio:

```bash
npx prisma studio
```

Это откроет веб-интерфейс для просмотра и редактирования данных в базе.
