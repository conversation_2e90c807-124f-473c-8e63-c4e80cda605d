import type { NextConfig } from 'next';

const nextConfig: NextConfig = {
  /* config options here */
  env: {
    // Make environment variables available to the browser
  },

  // Enable server-side environment variables
  serverRuntimeConfig: {
    CLAUDE_API_KEY: process.env.CLAUDE_API_KEY,
    GEMINI_API_KEY: process.env.GEMINI_API_KEY,
  },

  // Enable client-side environment variables (use with caution for API keys)
  publicRuntimeConfig: {
    // Public variables that are safe to expose to the client
    APP_ENV: process.env.NODE_ENV,
    NEXT_PUBLIC_ANALYSIS_MAX_TOKENS:
      process.env.NEXT_PUBLIC_ANALYSIS_MAX_TOKENS,
    NEXT_PUBLIC_ANALYSIS_BATCH_SIZE:
      process.env.NEXT_PUBLIC_ANALYSIS_BATCH_SIZE,
  },

  // Other common Next.js configurations
  reactStrictMode: true,
  images: {
    domains: ['localhost'],
  },
};

export default nextConfig;
